# core/views.py

import uuid
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics
from rest_framework.permissions import IsAuthenticated, AllowAny

# 确保引入了所有需要的模型和序列化器
from .models import User, Room
from .serializers import UserSerializer, RoomSerializer, CustomTokenObtainPairSerializer
from events.models import EventTemplate, EventStep # 确保引入 EventStep
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.tokens import RefreshToken


class APIRootView(APIView):
    def get(self, request, *args, **kwargs):
        return Response({"message": "Welcome to the Tuanzi API!"})

class HealthCheckView(APIView):
    def get(self, request, *args, **kwargs):
        return Response({"status": "ok"}, status=status.HTTP_200_OK)

class RegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [AllowAny]

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer


class RoomCreateView(APIView):
    """
    使用手写的APIView来创建房间，以完全控制创建和返回的逻辑。
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        template_id = request.data.get('template_id')
        if not template_id:
            return Response({"error": "Template ID is required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            template = EventTemplate.objects.get(id=template_id)
        except EventTemplate.DoesNotExist:
            return Response({"error": "Template not found."}, status=status.HTTP_404_NOT_FOUND)

        host = request.user

        # 保留您原有的订阅等级检查逻辑
        if host.subscription_level == host.SUBSCRIPTION_FREE:
            premium_steps = template.steps.filter(step_type__in=EventStep.PREMIUM_STEP_TYPES)
            if premium_steps.exists():
                premium_step_names = [step.get_step_type_display() for step in premium_steps]
                return Response({
                    "error": "此模板包含付费专属环节，请升级到Pro版本以使用。",
                    "premium_steps": premium_step_names,
                    "upgrade_required": True
                }, status=status.HTTP_403_FORBIDDEN)

        # --- 核心修复：重新引入 room_code 唯一性检查循环 ---
        while True:
            room_code = uuid.uuid4().hex.upper()[:6]
            if not Room.objects.filter(room_code=room_code).exists():
                break

        # 创建房间实例，并明确设置房主和已确保唯一的room_code
        new_room = Room.objects.create(
            host=host,
            event_template=template,
            room_code=room_code # 传递已验证唯一的 code
        )
        
        # 调用模型方法设置订阅限制
        new_room.set_limits_by_subscription(host)
        
        # 将房主自动添加到参与者列表
        new_room.participants.add(host)
        new_room.save()

        # 使用新创建的房间实例来序列化数据
        serializer = RoomSerializer(new_room)
        
        # 返回序列化后的数据
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class JoinRoomView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        room_code = request.data.get('room_code')
        if not room_code:
            return Response(
                {"error": "Room code is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        try:
            room = Room.objects.get(room_code__iexact=room_code)
        except Room.DoesNotExist:
            return Response(
                {"error": "Room not found."},
                status=status.HTTP_404_NOT_FOUND
            )
        user = request.user
        if room.participants.filter(id=user.id).exists():
            serializer = RoomSerializer(room)
            return Response(serializer.data, status=status.HTTP_200_OK)
        if room.participants.count() >= room.max_participants:
             return Response(
                {"error": f"Room is full. Maximum {room.max_participants} participants allowed."},
                status=status.HTTP_403_FORBIDDEN
            )
        room.participants.add(user)
        serializer = RoomSerializer(room)
        return Response(serializer.data, status=status.HTTP_200_OK)


class SubscriptionManagementView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        user = request.user
        return Response({
            'current_level': user.subscription_level,
            'username': user.username,
            'subscription_info': {
                'Free': {'max_participants': 10, 'duration_hours': 2},
                'Pro': {'max_participants': 500, 'duration_hours': 24},
                'Max': {'max_participants': 2000, 'duration_hours': 72},
            }
        })

    def post(self, request, *args, **kwargs):
        target_level = request.data.get('target_level')
        is_debug = request.data.get('is_debug', False)
        if target_level not in [User.SUBSCRIPTION_FREE, User.SUBSCRIPTION_PRO, User.SUBSCRIPTION_MAX]:
            return Response(
                {"error": "Invalid subscription level"},
                status=status.HTTP_400_BAD_REQUEST
            )
        user = request.user
        if is_debug and hasattr(request, 'META') and request.META.get('HTTP_X_DEBUG_MODE'):
            user.subscription_level = target_level
            user.save()
            refresh = RefreshToken.for_user(user)
            return Response({
                'message': f'Debug: Subscription level changed to {target_level}',
                'new_level': target_level,
                'access_token': str(refresh.access_token),
                'refresh_token': str(refresh)
            })
        return Response({
            'message': 'Payment integration not implemented yet',
        }, status=status.HTTP_501_NOT_IMPLEMENTED)
