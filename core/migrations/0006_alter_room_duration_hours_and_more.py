# Generated by Django 5.2.4 on 2025-07-11 16:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('core', '0005_room_duration_hours_room_max_participants'),
    ]

    operations = [
        migrations.AlterField(
            model_name='room',
            name='duration_hours',
            field=models.PositiveIntegerField(default=2, help_text='房间持续时长（小时）'),
        ),
        migrations.AlterField(
            model_name='room',
            name='max_participants',
            field=models.PositiveIntegerField(default=10, help_text='房间允许的最大参与人数'),
        ),
        migrations.AlterField(
            model_name='room',
            name='status',
            field=models.CharField(choices=[('WAITING', '等待中'), ('IN_PROGRESS', '进行中'), ('FINISHED', '已结束')], default='WAITING', max_length=20),
        ),
        migrations.AlterField(
            model_name='user',
            name='groups',
            field=models.ManyToManyField(blank=True, help_text='The groups this user belongs to.', related_name='core_user_set', related_query_name='user', to='auth.group', verbose_name='groups'),
        ),
        migrations.AlterField(
            model_name='user',
            name='subscription_level',
            field=models.CharField(choices=[('Free', '免费版'), ('Pro', 'Pro版'), ('Max', 'Max版')], default='Free', help_text='用户订阅等级，决定可用功能范围', max_length=10),
        ),
        migrations.CreateModel(
            name='RoomMembership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('host', '房主'), ('member', '成员')], default='member', max_length=10)),
                ('wins', models.PositiveIntegerField(default=0, help_text='在该房间内的胜利场次')),
                ('joined_at', models.DateTimeField(auto_now_add=True)),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='memberships', to='core.room')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='memberships', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-wins', 'joined_at'],
                'unique_together': {('user', 'room')},
            },
        ),
        migrations.AlterField(
            model_name='room',
            name='participants',
            field=models.ManyToManyField(blank=True, related_name='rooms_participated', through='core.RoomMembership', to=settings.AUTH_USER_MODEL),
        ),
        migrations.DeleteModel(
            name='GameSession',
        ),
    ]
