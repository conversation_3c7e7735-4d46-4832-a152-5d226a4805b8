# Generated by Django 5.2.4 on 2025-07-11 17:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('core', '0007_setup_user_groups'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='room',
            name='participants',
        ),
        migrations.AlterField(
            model_name='room',
            name='duration_hours',
            field=models.PositiveIntegerField(default=2, help_text='Room duration in hours'),
        ),
        migrations.AlterField(
            model_name='room',
            name='max_participants',
            field=models.PositiveIntegerField(default=10, help_text='Maximum number of participants allowed'),
        ),
        migrations.AlterField(
            model_name='room',
            name='status',
            field=models.CharField(choices=[('WAITING', 'Waiting for players'), ('IN_PROGRESS', 'In Progress'), ('FINISHED', 'Finished')], default='WAITING', max_length=20),
        ),
        migrations.AlterField(
            model_name='user',
            name='groups',
            field=models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='core_user_set', related_query_name='user', to='auth.group', verbose_name='groups'),
        ),
        migrations.AlterField(
            model_name='user',
            name='subscription_level',
            field=models.CharField(choices=[('Free', 'Free'), ('Pro', 'Pro'), ('Max', 'Max')], default='Free', help_text='用户订阅等级，决定可用功能范围', max_length=10),
        ),
        migrations.CreateModel(
            name='GameSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='game_sessions', to='core.room')),
            ],
        ),
        migrations.CreateModel(
            name='RoomParticipant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('joined_at', models.DateTimeField(auto_now_add=True, help_text='用户加入房间的时间')),
                ('last_active_at', models.DateTimeField(auto_now=True, help_text='用户最后活跃时间')),
                ('role', models.CharField(choices=[('participant', '参与者'), ('moderator', '协管员'), ('host', '房主')], default='participant', help_text='用户在房间中的角色', max_length=20)),
                ('score', models.IntegerField(default=0, help_text='用户在房间中的总得分')),
                ('is_ready', models.BooleanField(default=False, help_text='用户是否已准备开始游戏')),
                ('is_active', models.BooleanField(default=True, help_text='用户是否仍在房间中（未退出）')),
                ('custom_data', models.JSONField(blank=True, default=dict, help_text='自定义数据字段，用于存储特殊游戏状态等')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_participants', to='core.room')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_participations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '房间参与者',
                'verbose_name_plural': '房间参与者',
                'ordering': ['-joined_at'],
                'unique_together': {('room', 'user')},
            },
        ),
        migrations.DeleteModel(
            name='RoomMembership',
        ),
    ]
