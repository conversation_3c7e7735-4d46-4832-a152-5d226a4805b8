# Generated by Django 5.2.4 on 2025-07-11 17:31

from django.db import migrations
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType


def create_user_groups(apps, schema_editor):
    """
    创建标准用户组和权限
    """
    # 创建用户组
    groups_data = [
        {
            'name': 'Regular Users',
            'description': '普通用户组 - 基本功能权限'
        },
        {
            'name': 'Staff',
            'description': '工作人员组 - 管理部分功能'
        },
        {
            'name': 'Administrators',
            'description': '管理员组 - 高级管理权限'
        },
        {
            'name': 'Super Administrators',
            'description': '超级管理员组 - 所有权限'
        }
    ]

    created_groups = {}
    for group_data in groups_data:
        group, created = Group.objects.get_or_create(
            name=group_data['name'],
            defaults={'name': group_data['name']}
        )
        created_groups[group_data['name']] = group
        if created:
            print(f"Created group: {group_data['name']}")

    # 获取内容类型
    User = apps.get_model('core', 'User')
    Room = apps.get_model('core', 'Room')
    EventTemplate = apps.get_model('events', 'EventTemplate')

    user_ct = ContentType.objects.get_for_model(User)
    room_ct = ContentType.objects.get_for_model(Room)
    event_ct = ContentType.objects.get_for_model(EventTemplate)

    # 为不同组分配权限
    # Regular Users - 基本权限
    regular_permissions = [
        # 房间相关
        ('add_room', room_ct),
        ('view_room', room_ct),
        # 事件模板相关
        ('add_eventtemplate', event_ct),
        ('view_eventtemplate', event_ct),
        ('change_eventtemplate', event_ct),
        ('delete_eventtemplate', event_ct),
    ]

    # Staff - 工作人员权限
    staff_permissions = regular_permissions + [
        ('change_room', room_ct),
        ('view_user', user_ct),
    ]

    # Administrators - 管理员权限
    admin_permissions = staff_permissions + [
        ('delete_room', room_ct),
        ('change_user', user_ct),
    ]

    # Super Administrators - 超级管理员权限
    super_admin_permissions = admin_permissions + [
        ('add_user', user_ct),
        ('delete_user', user_ct),
    ]

    # 分配权限到组
    permission_assignments = [
        ('Regular Users', regular_permissions),
        ('Staff', staff_permissions),
        ('Administrators', admin_permissions),
        ('Super Administrators', super_admin_permissions),
    ]

    for group_name, permissions in permission_assignments:
        group = created_groups[group_name]
        for perm_codename, content_type in permissions:
            try:
                permission = Permission.objects.get(
                    codename=perm_codename,
                    content_type=content_type
                )
                group.permissions.add(permission)
            except Permission.DoesNotExist:
                print(f"Warning: Permission {perm_codename} not found for {content_type}")

    print("User groups and permissions setup completed.")


def reverse_user_groups(apps, schema_editor):
    """
    删除创建的用户组
    """
    group_names = ['Regular Users', 'Staff', 'Administrators', 'Super Administrators']
    for group_name in group_names:
        try:
            group = Group.objects.get(name=group_name)
            group.delete()
            print(f"Deleted group: {group_name}")
        except Group.DoesNotExist:
            pass


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0006_alter_room_duration_hours_and_more'),
        ('events', '0004_alter_eventstep_step_type'),  # 确保events应用的迁移已完成
    ]

    operations = [
        migrations.RunPython(create_user_groups, reverse_user_groups),
    ]
