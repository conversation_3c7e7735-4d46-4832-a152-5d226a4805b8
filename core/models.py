from django.db import models
from django.conf import settings
from django.contrib.auth.models import AbstractUser, Group
from events.models import EventTemplate # Import EventTemplate from the events app

class User(AbstractUser):
    """
    扩展的用户模型

    在Django默认用户模型基础上添加订阅功能：
    - 订阅等级管理
    - 基于订阅等级的功能权限控制
    """

    # 订阅等级常量定义
    SUBSCRIPTION_FREE = 'Free'  # 免费版：基础功能
    SUBSCRIPTION_PRO = 'Pro'    # Pro版：高级功能
    SUBSCRIPTION_MAX = 'Max'    # Max版：全功能

    SUBSCRIPTION_CHOICES = [
        (SUBSCRIPTION_FREE, 'Free'),
        (SUBSCRIPTION_PRO, 'Pro'),
        (SUBSCRIPTION_MAX, 'Max'),
    ]

    # 用户订阅等级字段
    subscription_level = models.CharField(
        max_length=10,
        choices=SUBSCRIPTION_CHOICES,
        default=SUBSCRIPTION_FREE,
        help_text='用户订阅等级，决定可用功能范围'
    )

    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text=('The groups this user belongs to. A user will get all permissions '
                   'granted to each of their groups.'),
        related_name="core_user_set",
        related_query_name="user",
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name="core_user_permissions_set",
        related_query_name="user",
    )

    def __str__(self):
        return f"{self.username} ({self.subscription_level})"

    # 用户组权限检查方法
    def is_in_group(self, group_name):
        """检查用户是否属于指定组"""
        return self.groups.filter(name=group_name).exists()

    def is_administrator(self):
        """检查用户是否为管理员（替代is_superuser）"""
        return self.is_in_group('Super Administrators') or self.is_in_group('Administrators')

    def is_staff_member(self):
        """检查用户是否为工作人员（替代is_staff）"""
        return self.is_administrator() or self.is_in_group('Staff')

    def get_user_role(self):
        """获取用户的主要角色"""
        if self.is_in_group('Super Administrators'):
            return 'super_admin'
        elif self.is_in_group('Administrators'):
            return 'admin'
        elif self.is_in_group('Staff'):
            return 'staff'
        else:
            return 'user'

class Room(models.Model):
    STATUS_WAITING = 'WAITING'
    STATUS_IN_PROGRESS = 'IN_PROGRESS' # Renamed from IN_GAME for clarity
    STATUS_FINISHED = 'FINISHED'
    STATUS_CHOICES = [
        (STATUS_WAITING, 'Waiting for players'),
        (STATUS_IN_PROGRESS, 'In Progress'),
        (STATUS_FINISHED, 'Finished'),
    ]

    room_code = models.CharField(max_length=10, unique=True, blank=True)
    host = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='hosted_rooms')
    # 注意：participants现在通过RoomParticipant中间模型管理
    # 可以通过 room.room_participants.all() 或 room.get_participants() 访问
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_WAITING)
    created_at = models.DateTimeField(auto_now_add=True)

    # --- NEW: Link Room to an EventTemplate ---
    # This is the core of the refactor. Every room is now based on a template.
    event_template = models.ForeignKey(EventTemplate, on_delete=models.SET_NULL, null=True, blank=True)

    # --- NEW: Track the current step of the event flow ---
    current_step_order = models.PositiveIntegerField(default=0)

    # --- NEW: Subscription-based limitations ---
    max_participants = models.PositiveIntegerField(default=10, help_text="Maximum number of participants allowed")
    duration_hours = models.PositiveIntegerField(default=2, help_text="Room duration in hours")

    def set_limits_by_subscription(self, user):
        """
        根据用户订阅等级设置房间限制

        参数：
        - user: 用户对象，包含订阅等级信息

        限制规则：
        - Free: 10人，2小时
        - Pro: 500人，24小时
        - Max: 2000人，72小时
        """
        if user.subscription_level == user.SUBSCRIPTION_FREE:
            self.max_participants = 10
            self.duration_hours = 2
        elif user.subscription_level == user.SUBSCRIPTION_PRO:
            self.max_participants = 500
            self.duration_hours = 24
        elif user.subscription_level == user.SUBSCRIPTION_MAX:
            self.max_participants = 2000  # 实际上相当于无限制
            self.duration_hours = 72

    def __str__(self):
        template_name = self.event_template.name if self.event_template else "No Template"
        return f"Room {self.room_code} ({template_name})"

    def get_participants(self):
        """获取所有活跃的参与者用户对象"""
        return User.objects.filter(
            user_participations__room=self,
            user_participations__is_active=True
        )

    def get_participant_count(self):
        """获取活跃参与者数量"""
        return self.room_participants.filter(is_active=True).count()

    def add_participant(self, user, role='participant'):
        """添加参与者到房间"""
        # 检查用户是否已经在房间中
        existing = self.room_participants.filter(user=user, is_active=True).first()
        if existing:
            return existing

        # 如果用户之前退出过，重新激活
        previous = self.room_participants.filter(user=user).first()
        if previous:
            previous.is_active = True
            previous.role = role
            previous.save()
            return previous

        # 创建新的参与关系 - 这里会在RoomParticipant定义后工作
        # 暂时返回None，稍后在视图中处理
        return None

    def remove_participant(self, user):
        """从房间中移除参与者（设为非活跃状态）"""
        participant = self.room_participants.filter(user=user, is_active=True).first()
        if participant:
            participant.is_active = False
            participant.save()
            return True
        return False

    def get_host_participant(self):
        """获取房主的参与者记录"""
        return self.room_participants.filter(role='host', is_active=True).first()

    def is_full(self):
        """检查房间是否已满"""
        return self.get_participant_count() >= self.max_participants

class RoomParticipant(models.Model):
    """
    玩家-房间关系中间模型

    整合了原来的简单多对多关系和得分系统，提供更丰富的功能：
    - 加入时间和活跃状态跟踪
    - 房间内角色权限管理
    - 游戏得分和状态管理
    - 为未来功能预留扩展空间
    """

    # 基本关系
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='room_participants')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='user_participations')

    # 时间信息
    joined_at = models.DateTimeField(auto_now_add=True, help_text='用户加入房间的时间')
    last_active_at = models.DateTimeField(auto_now=True, help_text='用户最后活跃时间')

    # 房间内角色权限
    ROLE_PARTICIPANT = 'participant'
    ROLE_MODERATOR = 'moderator'
    ROLE_HOST = 'host'

    ROLE_CHOICES = [
        (ROLE_PARTICIPANT, '参与者'),
        (ROLE_MODERATOR, '协管员'),
        (ROLE_HOST, '房主'),
    ]

    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default=ROLE_PARTICIPANT,
        help_text='用户在房间中的角色'
    )

    # 游戏相关信息
    score = models.IntegerField(default=0, help_text='用户在房间中的总得分')
    is_ready = models.BooleanField(default=False, help_text='用户是否已准备开始游戏')

    # 状态信息
    is_active = models.BooleanField(default=True, help_text='用户是否仍在房间中（未退出）')

    # 扩展字段（为未来功能预留）
    custom_data = models.JSONField(default=dict, blank=True, help_text='自定义数据字段，用于存储特殊游戏状态等')

    class Meta:
        unique_together = ('room', 'user')
        ordering = ['-joined_at']
        verbose_name = '房间参与者'
        verbose_name_plural = '房间参与者'

    def __str__(self):
        return f"{self.user.username} in Room {self.room.room_code} ({self.get_role_display()})"

    def can_manage_room(self):
        """检查用户是否有房间管理权限"""
        return self.role in [self.ROLE_HOST, self.ROLE_MODERATOR]

    def can_control_game(self):
        """检查用户是否可以控制游戏流程"""
        return self.role == self.ROLE_HOST

    def add_score(self, points):
        """增加得分"""
        self.score += points
        self.save(update_fields=['score'])

    def reset_score(self):
        """重置得分"""
        self.score = 0
        self.save(update_fields=['score'])


# Note: GameSession might become obsolete or be refactored later,
# as the game logic will be driven by EventSteps. We'll keep it for now.
class GameSession(models.Model):
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='game_sessions')
    is_active = models.BooleanField(default=True)
