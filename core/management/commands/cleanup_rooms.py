"""
房间清理管理命令

自动关闭过期房间并销毁长时间关闭的房间
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from core.models import Room
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '清理过期和长时间关闭的房间'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示将要执行的操作，不实际执行',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='显示详细输出',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        verbose = options['verbose']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - 不会实际执行操作')
            )
        
        # 1. 关闭过期的房间
        expired_rooms = Room.objects.filter(
            expires_at__lt=timezone.now(),
            status__in=[Room.STATUS_WAITING, Room.STATUS_IN_PROGRESS, Room.STATUS_FINISHED]
        )
        
        expired_count = expired_rooms.count()
        if expired_count > 0:
            if verbose or dry_run:
                self.stdout.write(f'发现 {expired_count} 个过期房间需要关闭:')
                for room in expired_rooms:
                    self.stdout.write(f'  - 房间 {room.room_code} (创建于: {room.created_at}, 过期于: {room.expires_at})')
            
            if not dry_run:
                with transaction.atomic():
                    for room in expired_rooms:
                        room.close_room()
                        if verbose:
                            self.stdout.write(f'已关闭房间: {room.room_code}')
                
                self.stdout.write(
                    self.style.SUCCESS(f'成功关闭 {expired_count} 个过期房间')
                )
            else:
                self.stdout.write(f'将关闭 {expired_count} 个过期房间')
        else:
            if verbose:
                self.stdout.write('没有发现过期房间')
        
        # 2. 销毁长时间关闭的房间
        rooms_to_destroy = Room.objects.filter(
            status=Room.STATUS_CLOSED
        )
        
        destroy_list = []
        for room in rooms_to_destroy:
            if room.is_ready_for_destruction():
                destroy_list.append(room)
        
        destroy_count = len(destroy_list)
        if destroy_count > 0:
            if verbose or dry_run:
                self.stdout.write(f'发现 {destroy_count} 个房间需要销毁:')
                for room in destroy_list:
                    self.stdout.write(f'  - 房间 {room.room_code} (关闭于: {room.closed_at})')
            
            if not dry_run:
                with transaction.atomic():
                    for room in destroy_list:
                        room_code = room.room_code
                        # 删除房间（会级联删除相关的RoomParticipant记录）
                        room.delete()
                        if verbose:
                            self.stdout.write(f'已销毁房间: {room_code}')
                
                self.stdout.write(
                    self.style.SUCCESS(f'成功销毁 {destroy_count} 个房间')
                )
            else:
                self.stdout.write(f'将销毁 {destroy_count} 个房间')
        else:
            if verbose:
                self.stdout.write('没有发现需要销毁的房间')
        
        # 3. 统计信息
        total_rooms = Room.objects.count()
        active_rooms = Room.objects.filter(
            status__in=[Room.STATUS_WAITING, Room.STATUS_IN_PROGRESS]
        ).count()
        closed_rooms = Room.objects.filter(status=Room.STATUS_CLOSED).count()
        
        if verbose:
            self.stdout.write('\n房间统计信息:')
            self.stdout.write(f'  总房间数: {total_rooms}')
            self.stdout.write(f'  活跃房间: {active_rooms}')
            self.stdout.write(f'  已关闭房间: {closed_rooms}')
        
        if not dry_run:
            logger.info(f'房间清理完成 - 关闭: {expired_count}, 销毁: {destroy_count}')
        
        self.stdout.write(
            self.style.SUCCESS('房间清理任务完成')
        )
