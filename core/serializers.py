"""
Django REST Framework 序列化器
处理API数据的序列化和反序列化
"""

from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from .models import User, Room
from events.models import EventTemplate

class UserSerializer(serializers.ModelSerializer):
    """
    用户序列化器
    """
    class Meta:
        model = User
        fields = ('id', 'username', 'password', 'subscription_level')
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        user = User.objects.create_user(
            username=validated_data['username'],
            password=validated_data['password']
        )
        return user


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    自定义JWT Token序列化器
    """
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        token['username'] = user.username
        token['subscription_level'] = user.subscription_level
        return token

class RoomSerializer(serializers.ModelSerializer):
    """
    房间序列化器
    """
    # --- 核心修复：使用 SerializerMethodField ---
    # 1. host 字段：
    #    - 我们定义一个名为 'host' 的只读字段。
    #    - 它的值将由下面我们定义的 get_host 方法来提供。
    host = serializers.SerializerMethodField()
    
    # 2. participants 字段 (优化):
    #    - 同样，我们也用自定义方法来确保返回的是用户名列表。
    participants = serializers.SerializerMethodField()

    event_template = serializers.PrimaryKeyRelatedField(
        queryset=EventTemplate.objects.all(),
        write_only=True
    )

    class Meta:
        model = Room
        # 确保 'host' 和 'participants' 在字段列表中
        fields = ['id', 'room_code', 'host', 'participants', 'status', 'event_template']
        read_only_fields = ['room_code', 'status']

    def get_host(self, obj):
        """
        这个方法的名字必须是 get_<field_name>。
        它接收房间对象(obj)，并返回其房主的用户名。
        这确保了返回的永远是字符串，而不是ID。
        """
        if obj.host:
            return obj.host.username
        return None

    def get_participants(self, obj):
        """
        这个方法返回一个由所有参与者用户名组成的列表。
        """
        return [user.username for user in obj.participants.all()]

