"""
测试新的面向对象环节处理器
"""
import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch
from django.test import TestCase
from django.contrib.auth import get_user_model
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async

from .models import Room, RoomParticipant
from events.models import EventTemplate, EventStep
from games.models import Game, PictionaryGame
from .event_handlers import (
    BaseEventHandler,
    PictionaryEventHandler,
    FreeChatEventHandler,
    EventHandlerFactory
)
from .consumers import RoomConsumer

User = get_user_model()


class MockConsumer:
    """模拟的 WebSocket 消费者"""
    def __init__(self):
        self.channel_layer = AsyncMock()
        self.room_group_name = "test_room_group"
        self.send_error = AsyncMock()


class BaseEventHandlerTest(TestCase):
    """基础环节处理器测试"""
    
    def setUp(self):
        self.room_code = "TEST123"
        self.consumer = MockConsumer()
        
    def test_abstract_methods(self):
        """测试抽象方法不能直接实例化"""
        with self.assertRaises(TypeError):
            BaseEventHandler(self.room_code, self.consumer)
    
    async def test_common_methods(self):
        """测试通用方法"""
        # 创建一个具体的处理器来测试通用方法
        handler = PictionaryEventHandler(self.room_code, self.consumer)
        
        # 测试广播方法
        await handler.broadcast_to_room("test_type", {"test": "data"})
        self.consumer.channel_layer.group_send.assert_called_once()
        
        # 测试错误发送方法
        await handler.send_error_to_user("test error")
        self.consumer.send_error.assert_called_once_with("test error")


class PictionaryEventHandlerTest(TestCase):
    """你画我猜环节处理器测试"""
    
    def setUp(self):
        self.user1 = User.objects.create_user(username='player1', password='test123')
        self.user2 = User.objects.create_user(username='player2', password='test123')
        self.host = User.objects.create_user(username='host', password='test123')
        
        self.template = EventTemplate.objects.create(
            name='Test Template',
            creator=self.host
        )
        
        self.step = EventStep.objects.create(
            template=self.template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300
        )
        
        self.room = Room.objects.create(
            room_code='TEST123',
            host=self.host,
            event_template=self.template
        )
        # 添加参与者到房间（使用新的中间模型）
        self.room.add_participant(self.user1)
        self.room.add_participant(self.user2)
        
        self.consumer = MockConsumer()
        self.handler = PictionaryEventHandler('TEST123', self.consumer)
    
    async def test_start_step_success(self):
        """测试成功启动你画我猜游戏"""
        game_data, error = await self.handler.start_step(self.room, self.step)
        
        self.assertIsNone(error)
        self.assertIsNotNone(game_data)
        self.assertIn('drawer', game_data)
        self.assertIn('word', game_data)
        self.assertIn('duration', game_data)
        self.assertEqual(game_data['duration'], 300)
    
    async def test_start_step_no_players(self):
        """测试没有玩家时启动游戏"""
        self.room.participants.clear()
        
        game_data, error = await self.handler.start_step(self.room, self.step)
        
        self.assertIsNone(game_data)
        self.assertEqual(error, "至少需要1名玩家才能开始游戏。")
    
    async def test_handle_message_correct_guess(self):
        """测试正确猜词"""
        # 先启动游戏
        await self.handler.start_step(self.room, self.step)
        
        # 获取游戏数据
        game = await database_sync_to_async(
            PictionaryGame.objects.select_related('game__room', 'current_drawer').get
        )(game__room=self.room, game__is_active=True)
        
        # 模拟非绘画者猜词
        guesser = self.user1 if game.current_drawer != self.user1 else self.user2
        payload = {'message': game.current_word}
        
        with patch('core.consumers.update_scores') as mock_update_scores, \
             patch('core.consumers.end_pictionary_round') as mock_end_round:
            
            mock_update_scores.return_value = {'player1': 10, 'player2': 5}
            mock_end_round.return_value = Room.STATUS_WAITING
            
            handled = await self.handler.handle_message(guesser, payload)
            
            self.assertTrue(handled)
            mock_update_scores.assert_called_once()
            mock_end_round.assert_called_once()
    
    async def test_handle_message_drawer_cannot_guess(self):
        """测试绘画者不能猜词"""
        # 先启动游戏
        await self.handler.start_step(self.room, self.step)
        
        # 获取游戏数据
        game = await database_sync_to_async(
            PictionaryGame.objects.select_related('game__room', 'current_drawer').get
        )(game__room=self.room, game__is_active=True)
        
        # 模拟绘画者尝试猜词
        payload = {'message': game.current_word}
        handled = await self.handler.handle_message(game.current_drawer, payload)
        
        self.assertTrue(handled)  # 消息被处理（忽略）
    
    async def test_handle_timeout(self):
        """测试超时处理"""
        # 先启动游戏
        await self.handler.start_step(self.room, self.step)
        
        with patch('core.consumers.get_pictionary_game') as mock_get_game, \
             patch('core.consumers.end_pictionary_round') as mock_end_round:
            
            mock_game = MagicMock()
            mock_game.current_word = "测试词汇"
            mock_get_game.return_value = mock_game
            mock_end_round.return_value = Room.STATUS_WAITING
            
            await self.handler.handle_timeout()
            
            mock_end_round.assert_called_once()
            self.handler.consumer.channel_layer.group_send.assert_called()
    
    async def test_handle_drawing_data(self):
        """测试处理绘图数据"""
        # 先启动游戏
        await self.handler.start_step(self.room, self.step)
        
        # 获取游戏数据
        game = await database_sync_to_async(
            PictionaryGame.objects.select_related('game__room', 'current_drawer').get
        )(game__room=self.room, game__is_active=True)
        
        payload = {'path_data': {'id': '1', 'path': 'M10,10 L20,20', 'color': '#000000'}}
        
        # 测试绘画者发送绘图数据
        handled = await self.handler.handle_custom_action('send_drawing', game.current_drawer, payload)
        
        self.assertTrue(handled)
        self.handler.consumer.channel_layer.group_send.assert_called()
    
    async def test_handle_restart(self):
        """测试重启游戏"""
        with patch.object(self.handler, 'get_room_with_template') as mock_get_room:
            mock_get_room.return_value = self.room
            
            with patch.object(self.handler, 'start_step') as mock_start_step:
                mock_start_step.return_value = ({'test': 'data'}, None)
                
                game_data, error = await self.handler.handle_restart(self.host, {})
                
                self.assertIsNone(error)
                self.assertIsNotNone(game_data)
                mock_start_step.assert_called_once()


class FreeChatEventHandlerTest(TestCase):
    """自由聊天环节处理器测试"""
    
    def setUp(self):
        self.host = User.objects.create_user(username='host', password='test123')
        
        self.template = EventTemplate.objects.create(
            name='Test Template',
            creator=self.host
        )
        
        self.step = EventStep.objects.create(
            template=self.template,
            order=1,
            step_type=EventStep.STEP_FREE_CHAT,
            duration=600
        )
        
        self.room = Room.objects.create(
            room_code='TEST123',
            host=self.host,
            event_template=self.template
        )
        
        self.consumer = MockConsumer()
        self.handler = FreeChatEventHandler('TEST123', self.consumer)
    
    async def test_start_step(self):
        """测试启动自由聊天"""
        game_data, error = await self.handler.start_step(self.room, self.step)
        
        self.assertIsNone(error)
        self.assertIsNotNone(game_data)
        self.assertEqual(game_data['room_status'], Room.STATUS_IN_PROGRESS)
        self.assertEqual(game_data['step_info']['step_type'], EventStep.STEP_FREE_CHAT)
    
    async def test_handle_message(self):
        """测试消息处理（应该返回False让常规处理继续）"""
        payload = {'message': 'Hello everyone!'}
        handled = await self.handler.handle_message(self.host, payload)
        
        self.assertFalse(handled)
    
    async def test_handle_timeout(self):
        """测试超时处理"""
        with patch.object(self.handler, 'get_room_with_template') as mock_get_room:
            mock_get_room.return_value = self.room
            
            await self.handler.handle_timeout()
            
            # 验证房间状态被更新
            self.room.refresh_from_db()
            self.assertEqual(self.room.status, Room.STATUS_WAITING)
            
            # 验证广播被调用
            self.handler.consumer.channel_layer.group_send.assert_called()


class EventHandlerFactoryTest(TestCase):
    """环节处理器工厂测试"""
    
    def setUp(self):
        self.consumer = MockConsumer()
    
    def test_create_pictionary_handler(self):
        """测试创建你画我猜处理器"""
        handler = EventHandlerFactory.create_handler(
            EventStep.STEP_GAME_PICTIONARY, 'TEST123', self.consumer
        )
        
        self.assertIsInstance(handler, PictionaryEventHandler)
        self.assertEqual(handler.room_code, 'TEST123')
        self.assertEqual(handler.consumer, self.consumer)
    
    def test_create_free_chat_handler(self):
        """测试创建自由聊天处理器"""
        handler = EventHandlerFactory.create_handler(
            EventStep.STEP_FREE_CHAT, 'TEST123', self.consumer
        )
        
        self.assertIsInstance(handler, FreeChatEventHandler)
        self.assertEqual(handler.room_code, 'TEST123')
        self.assertEqual(handler.consumer, self.consumer)
    
    def test_create_unknown_handler(self):
        """测试创建未知类型处理器"""
        handler = EventHandlerFactory.create_handler(
            'UNKNOWN_TYPE', 'TEST123', self.consumer
        )
        
        self.assertIsNone(handler)
    
    def test_get_supported_types(self):
        """测试获取支持的类型"""
        supported_types = EventHandlerFactory.get_supported_types()
        
        self.assertIn(EventStep.STEP_GAME_PICTIONARY, supported_types)
        self.assertIn(EventStep.STEP_FREE_CHAT, supported_types)
        self.assertEqual(len(supported_types), 2)


# 异步测试运行器
def run_async_test(test_func):
    """运行异步测试的辅助函数"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(test_func())
    finally:
        loop.close()


# 为异步测试方法添加装饰器
for test_class in [BaseEventHandlerTest, PictionaryEventHandlerTest, FreeChatEventHandlerTest]:
    for attr_name in dir(test_class):
        attr = getattr(test_class, attr_name)
        if attr_name.startswith('test_') and asyncio.iscoroutinefunction(attr):
            # 将异步测试方法包装为同步方法
            def make_sync_test(async_test):
                def sync_test(self):
                    return run_async_test(lambda: async_test(self))
                return sync_test
            
            setattr(test_class, attr_name, make_sync_test(attr))
