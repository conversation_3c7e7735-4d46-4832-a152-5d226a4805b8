# your_app/utils.py

import logging
from typing import Optional
from channels.db import database_sync_to_async
from .models import Room
from events.models import EventStep

logger = logging.getLogger(__name__)

@database_sync_to_async
def advance_to_next_step(room: Room) -> Optional[EventStep]:
    """
    将房间推进到下一个环节，并返回下一个环节的对象。
    如果没有更多环节，则返回 None。
    """
    if room.status == Room.STATUS_FINISHED:
        return None

    # 获取当前环节之后的所有步骤，并按顺序排序
    next_steps = room.event_template.steps.filter(
        order__gt=room.current_step_order
    ).order_by('order')

    next_step = next_steps.first()

    if next_step:
        # 如果有下一个环节，更新房间状态
        room.current_step_order = next_step.order
        room.status = Room.STATUS_IN_PROGRESS
        logger.info(f"房间 {room.room_code} 已推进到环节 {next_step.order}: {next_step.step_type}")
    else:
        # 如果没有更多环节，将房间标记为已结束
        room.status = Room.STATUS_FINISHED
        logger.info(f"房间 {room.room_code} 已完成所有环节。")

    # 保存对房间状态的更改
    room.save()
    return next_step

@database_sync_to_async
def save_room(room: Room):
    """
    异步地保存房间对象到数据库。
    """
    try:
        room.save()
        logger.debug(f"成功保存房间 {room.room_code} 的状态。")
    except Exception as e:
        logger.error(f"保存房间 {room.room_code} 时出错: {e}")

# get_room_with_template 方法已被 RoomConsumer 内的 get_room_with_host 替代，
# 因此可以从这个文件中移除，以保持代码整洁。

