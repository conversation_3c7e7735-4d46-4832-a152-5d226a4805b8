import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple, TYPE_CHECKING

from events.models import EventStep
from ..models import Room

# CHANGE: Use TYPE_CHECKING to break the circular dependency.
# This import will only be processed by type checkers (like mypy),
# not by the Python interpreter at runtime.
if TYPE_CHECKING:
    from ..consumers import RoomConsumer, get_room_with_host


class BaseEventHandler(ABC):
    """
    所有环节处理器的基础抽象类。
    定义了环节处理的统一接口和通用功能。
    """

    # CHANGE: The type hint 'RoomConsumer' is now a forward reference that is
    # resolved by the TYPE_CHECKING block above, breaking the runtime import cycle.
    def __init__(self, room_code: str, consumer: 'RoomConsumer'):
        self.room_code = room_code
        self.consumer = consumer
        self.logger = logging.getLogger(f"{self.__class__.__name__}")

    @abstractmethod
    async def start_step(self, room: Room, step: EventStep) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        启动环节的抽象方法。

        Args:
            room: 房间对象
            step: 环节步骤对象

        Returns:
            Tuple[payload_data, error_message]: 成功时返回(数据, None)，失败时返回(None, 错误信息)
        """
        pass

    @abstractmethod
    async def handle_message(self, user, payload: Dict[str, Any]) -> bool:
        """
        处理聊天消息的抽象方法。

        Args:
            user: 发送消息的用户
            payload: 消息载荷

        Returns:
            bool: True表示消息已被处理，False表示应继续常规处理
        """
        pass

    @abstractmethod
    async def handle_timeout(self) -> None:
        """
        处理环节超时的抽象方法。
        """
        pass

    async def handle_restart(self, user, payload: Dict[str, Any]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        处理重启请求的默认实现。子类可以重写此方法。
        """
        _ = user, payload
        return None, "此环节不支持重启功能"

    async def handle_custom_action(self, action: str, user, payload: Dict[str, Any]) -> bool:
        """
        处理自定义动作的默认实现。子类可以重写此方法。
        """
        _ = action, user, payload
        return False

    async def cleanup(self) -> None:
        """
        清理资源的默认实现。子类可以重写此方法。
        """
        pass

    # --- Helper Methods ---

    # This method now correctly uses the function imported from utils.py
    async def get_room_with_template(self) -> Optional[Room]:
        """获取带有模板的房间对象"""
        return await get_room_with_host(self.room_code)

    async def broadcast_to_room(self, message_type: str, payload: Dict[str, Any]) -> None:
        """向房间广播消息"""
        # This works because self.consumer is a real RoomConsumer instance at runtime
        await self.consumer.channel_layer.group_send(
            self.consumer.room_group_name,
            {'type': message_type, 'payload': payload}
        )

    async def send_error_to_user(self, message: str) -> None:
        """向用户发送错误消息"""
        await self.consumer.send_error(message)
